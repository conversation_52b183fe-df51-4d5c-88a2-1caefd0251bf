fix: resolve Radix UI Select component empty string value error

Fixed the "A <Select.Item /> must have a value prop that is not an empty string" 
error that was occurring in both services.tsx and blog.tsx pages when using 
the "All Categories" option in category filter dropdowns.

## Changes Made:

### Core Fix:
- Added SELECT_ALL_CATEGORIES constant in utils.ts with value 'all-categories'
- Replaced empty string values with SELECT_ALL_CATEGORIES constant in SelectItem components

### Services Page (services.tsx):
- Updated "All Categories" SelectItem to use SELECT_ALL_CATEGORIES value
- Modified filtering logic to exclude category parameter when SELECT_ALL_CATEGORIES is selected
- Updated badge display logic to hide category badge for SELECT_ALL_CATEGORIES
- Updated clearFilters function to set SELECT_ALL_CATEGORIES instead of empty string
- Updated hasActiveFilters logic to properly handle SELECT_ALL_CATEGORIES

### Blog Page (blog.tsx):
- Updated "All Categories" SelectItem to use SELECT_ALL_CATEGORIES value
- Modified filtering logic to exclude category_id parameter when SELECT_ALL_CATEGORIES is selected
- Updated badge display logic to hide category badge for SELECT_ALL_CATEGORIES
- Updated clearFilters function to set SELECT_ALL_CATEGORIES instead of empty string
- Updated hasActiveFilters logic to properly handle SELECT_ALL_CATEGORIES

### Tests:
- Updated existing test files to use SELECT_ALL_CATEGORIES constant
- Created comprehensive test suite (select-fix.test.tsx) to verify:
  - SelectItem components render with non-empty values
  - Filtering logic works correctly with new constant
  - Badge display logic functions properly
  - SELECT_ALL_CATEGORIES constant validation

## Technical Details:

The Radix UI Select component requires all SelectItem values to be non-empty 
strings because empty strings are reserved for clearing the selection and 
showing placeholders. This fix maintains the same user experience while 
ensuring compatibility with Radix UI requirements.

The filtering logic was updated to treat SELECT_ALL_CATEGORIES the same as 
an empty string for backend compatibility, ensuring no breaking changes to 
the API or user functionality.

## Testing:

- Manual testing confirmed Select components work without console errors
- Category filtering functionality preserved on both services and blog pages
- Badge display and clear filters functionality working correctly
- No regressions introduced to existing functionality

Fixes: Radix UI Select component error in browser console
