import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { router } from '@inertiajs/react';
import Services from '@/pages/public/services';
import Blog from '@/pages/public/blog';
import { SELECT_ALL_CATEGORIES } from '@/lib/utils';

// Mock the router and other Inertia components
vi.mock('@inertiajs/react', async () => {
  const actual = await vi.importActual('@inertiajs/react');
  return {
    ...actual,
    router: {
      get: vi.fn(),
    },
    usePage: () => ({
      props: {
        auth: { user: null },
      },
      url: '/services',
      component: 'Services',
    }),
    Link: ({ children, href, ...props }: { children: React.ReactNode; href: string; [key: string]: unknown }) => (
      <a href={href} {...props}>
        {children}
      </a>
    ),
  };
});

// Mock the PublicLayout
vi.mock('@/layouts/public-layout', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="public-layout">{children}</div>,
}));

describe('Select Component Fix Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Services Page Select Component', () => {
    const mockServices = [
      {
        id: 1,
        title: 'Meta Ads Management',
        slug: 'meta-ads-management',
        description: 'Professional Meta advertising management',
        category: 'Meta Advertising',
        price_range: '$500 - $2000',
        features: ['Campaign Setup', 'Optimization', 'Reporting'],
        is_active: true,
      },
    ];

    const mockCategories = ['Meta Advertising', 'Tracking & Analytics'];

    const mockFilters = {
      search: '',
      category: SELECT_ALL_CATEGORIES,
      sort_by: 'sort_order',
      sort_direction: 'asc',
    };

    const mockMeta = {
      title: 'Our Services - ConvertOKit',
      description: 'Professional Meta advertising and web analytics services',
    };

    const defaultProps = {
      services: mockServices,
      categories: mockCategories,
      filters: mockFilters,
      meta: mockMeta,
    };

    it('should render SelectItem with non-empty value for All Categories', async () => {
      const user = userEvent.setup();
      render(<Services {...defaultProps} />);

      // Click the category dropdown
      const categoryTrigger = screen.getByText('All Categories');
      await user.click(categoryTrigger);

      // Check that the "All Categories" option is rendered
      const allCategoriesOption = screen.getByText('All Categories');
      expect(allCategoriesOption).toBeInTheDocument();

      // The SelectItem should have a non-empty value (SELECT_ALL_CATEGORIES)
      // This test ensures the Radix UI error doesn't occur
      expect(allCategoriesOption.closest('[data-radix-collection-item]')).toHaveAttribute('data-value', SELECT_ALL_CATEGORIES);
    });

    it('should not send category parameter when All Categories is selected', async () => {
      const user = userEvent.setup();
      render(<Services {...defaultProps} />);

      // Type in search to trigger a filter change
      const searchInput = screen.getByPlaceholderText('Search services...');
      await user.type(searchInput, 'Meta');

      await waitFor(() => {
        expect(router.get).toHaveBeenCalledWith(
          '/services',
          expect.objectContaining({ search: 'Meta' }),
          expect.objectContaining({
            preserveState: true,
            preserveScroll: true,
          })
        );
      });

      // Verify that category parameter is not included when SELECT_ALL_CATEGORIES is selected
      const mockGet = vi.mocked(router.get);
      const lastCall = mockGet.mock.calls[mockGet.mock.calls.length - 1];
      expect(lastCall[1]).not.toHaveProperty('category');
    });

    it('should send category parameter when a specific category is selected', async () => {
      const user = userEvent.setup();
      render(<Services {...defaultProps} />);

      // Click the category dropdown
      const categoryTrigger = screen.getByText('All Categories');
      await user.click(categoryTrigger);

      // Select a specific category
      const metaCategory = screen.getByText('Meta Advertising');
      await user.click(metaCategory);

      await waitFor(() => {
        expect(router.get).toHaveBeenCalledWith(
          '/services',
          expect.objectContaining({ category: 'Meta Advertising' }),
          expect.objectContaining({
            preserveState: true,
            preserveScroll: true,
          })
        );
      });
    });

    it('should not show category badge when All Categories is selected', () => {
      render(<Services {...defaultProps} />);

      // Should not show category badge when SELECT_ALL_CATEGORIES is selected
      expect(screen.queryByText(/Category:/)).not.toBeInTheDocument();
    });

    it('should show category badge when a specific category is selected', () => {
      const propsWithCategory = {
        ...defaultProps,
        filters: {
          ...mockFilters,
          category: 'Meta Advertising',
        },
      };

      render(<Services {...propsWithCategory} />);

      // Should show category badge when a specific category is selected
      expect(screen.getByText('Category: Meta Advertising')).toBeInTheDocument();
    });
  });

  describe('Blog Page Select Component', () => {
    const mockBlogPosts = {
      data: [
        {
          id: 1,
          title: 'Meta Ads Best Practices',
          slug: 'meta-ads-best-practices',
          excerpt: 'Learn the best practices for Meta advertising campaigns',
          content: 'Full content here...',
          published_at: '2024-01-15',
          author: { name: 'John Doe' },
          category: { id: 1, name: 'Meta Advertising' },
          views_count: 150,
        },
      ],
    };

    const mockCategories = [
      { id: 1, name: 'Meta Advertising' },
      { id: 2, name: 'Tracking & Analytics' },
    ];

    const mockFilters = {
      search: '',
      category_id: SELECT_ALL_CATEGORIES,
      sort_by: 'published_at',
      sort_direction: 'desc',
    };

    const mockMeta = {
      title: 'Blog - ConvertOKit',
      description: 'Digital marketing insights and strategies',
    };

    const defaultProps = {
      blogPosts: mockBlogPosts,
      categories: mockCategories,
      filters: mockFilters,
      meta: mockMeta,
    };

    it('should render SelectItem with non-empty value for All Categories', async () => {
      const user = userEvent.setup();
      render(<Blog {...defaultProps} />);

      // Click the category dropdown
      const categoryTrigger = screen.getByText('All Categories');
      await user.click(categoryTrigger);

      // Check that the "All Categories" option is rendered
      const allCategoriesOption = screen.getByText('All Categories');
      expect(allCategoriesOption).toBeInTheDocument();

      // The SelectItem should have a non-empty value (SELECT_ALL_CATEGORIES)
      expect(allCategoriesOption.closest('[data-radix-collection-item]')).toHaveAttribute('data-value', SELECT_ALL_CATEGORIES);
    });

    it('should not send category_id parameter when All Categories is selected', async () => {
      const user = userEvent.setup();
      
      // Mock window.location.search
      Object.defineProperty(window, 'location', {
        value: { search: '' },
        writable: true,
      });

      render(<Blog {...defaultProps} />);

      // Type in search to trigger a filter change
      const searchInput = screen.getByPlaceholderText('Search articles...');
      await user.type(searchInput, 'Meta');

      await waitFor(() => {
        expect(router.get).toHaveBeenCalledWith(
          '/blog',
          expect.objectContaining({ search: 'Meta' }),
          expect.objectContaining({
            preserveState: true,
            preserveScroll: true,
          })
        );
      });

      // Verify that category_id parameter is not included when SELECT_ALL_CATEGORIES is selected
      const mockGet = vi.mocked(router.get);
      const lastCall = mockGet.mock.calls[mockGet.mock.calls.length - 1];
      expect(lastCall[1]).not.toHaveProperty('category_id');
    });

    it('should send category_id parameter when a specific category is selected', async () => {
      const user = userEvent.setup();
      
      // Mock window.location.search
      Object.defineProperty(window, 'location', {
        value: { search: '' },
        writable: true,
      });

      render(<Blog {...defaultProps} />);

      // Click the category dropdown
      const categoryTrigger = screen.getByText('All Categories');
      await user.click(categoryTrigger);

      // Select a specific category
      const metaCategory = screen.getByText('Meta Advertising');
      await user.click(metaCategory);

      await waitFor(() => {
        expect(router.get).toHaveBeenCalledWith(
          '/blog',
          expect.objectContaining({ category_id: '1' }),
          expect.objectContaining({
            preserveState: true,
            preserveScroll: true,
          })
        );
      });
    });

    it('should not show category badge when All Categories is selected', () => {
      render(<Blog {...defaultProps} />);

      // Should not show category badge when SELECT_ALL_CATEGORIES is selected
      expect(screen.queryByText(/Category:/)).not.toBeInTheDocument();
    });

    it('should show category badge when a specific category is selected', () => {
      const propsWithCategory = {
        ...defaultProps,
        filters: {
          ...mockFilters,
          category_id: '1',
        },
      };

      render(<Blog {...propsWithCategory} />);

      // Should show category badge when a specific category is selected
      expect(screen.getByText('Category: Meta Advertising')).toBeInTheDocument();
    });
  });

  describe('SELECT_ALL_CATEGORIES constant', () => {
    it('should be a non-empty string', () => {
      expect(SELECT_ALL_CATEGORIES).toBeTruthy();
      expect(typeof SELECT_ALL_CATEGORIES).toBe('string');
      expect(SELECT_ALL_CATEGORIES.length).toBeGreaterThan(0);
    });

    it('should be different from empty string', () => {
      expect(SELECT_ALL_CATEGORIES).not.toBe('');
    });
  });
});
