import React from 'react';
import { render } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import SeoHead from '@/components/seo-head';

// Mock the Head component to render children directly in the container
vi.mock('@inertiajs/react', () => ({
  Head: ({ children, title }: { children?: React.ReactNode; title?: string }) => {
    // Render title and children directly in the container for testing
    return (
      <>
        {title && <title>{title}</title>}
        {children}
      </>
    );
  },
}));

describe('SeoHead Component', () => {
  const mockMeta = {
    title: 'Test Page Title',
    description: 'Test page description',
    keywords: 'test, seo, meta',
    canonical: 'https://example.com/test',
    og_title: 'Test OG Title',
    og_description: 'Test OG Description',
    og_image: 'https://example.com/image.jpg',
    og_type: 'website',
    twitter_card: 'summary_large_image',
    twitter_title: 'Test Twitter Title',
    twitter_description: 'Test Twitter Description',
    robots: 'index, follow',
  };

  const mockStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'ConvertOKit',
    description: 'Meta advertising and web analytics specialists',
  };

  const mockBreadcrumbs = [
    { name: 'Home', url: 'https://example.com/' },
    { name: 'Services', url: 'https://example.com/services' },
    { name: 'Meta Ads', url: 'https://example.com/services/meta-ads' },
  ];

  it('renders basic meta tags correctly', () => {
    const { container } = render(<SeoHead meta={mockMeta} />);

    expect(container.querySelector('title')).toHaveTextContent('Test Page Title');
    expect(container.querySelector('meta[name="description"]')).toHaveAttribute(
      'content',
      'Test page description'
    );
    expect(container.querySelector('meta[name="keywords"]')).toHaveAttribute(
      'content',
      'test, seo, meta'
    );
    expect(container.querySelector('link[rel="canonical"]')).toHaveAttribute(
      'href',
      'https://example.com/test'
    );
  });

  it('renders Open Graph meta tags correctly', () => {
    const { container } = render(<SeoHead meta={mockMeta} />);

    expect(container.querySelector('meta[property="og:title"]')).toHaveAttribute(
      'content',
      'Test OG Title'
    );
    expect(container.querySelector('meta[property="og:description"]')).toHaveAttribute(
      'content',
      'Test OG Description'
    );
    expect(container.querySelector('meta[property="og:image"]')).toHaveAttribute(
      'content',
      'https://example.com/image.jpg'
    );
    expect(container.querySelector('meta[property="og:type"]')).toHaveAttribute(
      'content',
      'website'
    );
  });

  it('renders Twitter Card meta tags correctly', () => {
    const { container } = render(<SeoHead meta={mockMeta} />);

    expect(container.querySelector('meta[name="twitter:card"]')).toHaveAttribute(
      'content',
      'summary_large_image'
    );
    expect(container.querySelector('meta[name="twitter:title"]')).toHaveAttribute(
      'content',
      'Test Twitter Title'
    );
    expect(container.querySelector('meta[name="twitter:description"]')).toHaveAttribute(
      'content',
      'Test Twitter Description'
    );
  });

  it('renders robots meta tag correctly', () => {
    const { container } = render(<SeoHead meta={mockMeta} />);

    expect(container.querySelector('meta[name="robots"]')).toHaveAttribute(
      'content',
      'index, follow'
    );
  });

  it('renders structured data correctly', () => {
    const { container } = render(
      <SeoHead meta={mockMeta} structuredData={mockStructuredData} />
    );

    const structuredDataScript = container.querySelector(
      'script[type="application/ld+json"]'
    );
    expect(structuredDataScript).toBeInTheDocument();
    expect(structuredDataScript?.textContent).toBe(
      JSON.stringify(mockStructuredData)
    );
  });

  it('renders breadcrumb structured data correctly', () => {
    const { container } = render(
      <SeoHead meta={mockMeta} breadcrumbs={mockBreadcrumbs} />
    );

    const scripts = container.querySelectorAll('script[type="application/ld+json"]');
    expect(scripts).toHaveLength(1);

    const breadcrumbScript = Array.from(scripts).find(script => {
      const content = JSON.parse(script.textContent || '{}');
      return content['@type'] === 'BreadcrumbList';
    });

    expect(breadcrumbScript).toBeInTheDocument();
    
    const breadcrumbData = JSON.parse(breadcrumbScript?.textContent || '{}');
    expect(breadcrumbData['@context']).toBe('https://schema.org');
    expect(breadcrumbData['@type']).toBe('BreadcrumbList');
    expect(breadcrumbData.itemListElement).toHaveLength(3);
    expect(breadcrumbData.itemListElement[0].name).toBe('Home');
    expect(breadcrumbData.itemListElement[0].item).toBe('https://example.com/');
  });

  it('renders both structured data and breadcrumbs correctly', () => {
    const { container } = render(
      <SeoHead
        meta={mockMeta}
        structuredData={mockStructuredData}
        breadcrumbs={mockBreadcrumbs}
      />
    );

    const scripts = container.querySelectorAll('script[type="application/ld+json"]');
    expect(scripts).toHaveLength(2);

    const organizationScript = Array.from(scripts).find(script => {
      const content = JSON.parse(script.textContent || '{}');
      return content['@type'] === 'Organization';
    });

    const breadcrumbScript = Array.from(scripts).find(script => {
      const content = JSON.parse(script.textContent || '{}');
      return content['@type'] === 'BreadcrumbList';
    });

    expect(organizationScript).toBeInTheDocument();
    expect(breadcrumbScript).toBeInTheDocument();
  });

  it('handles missing optional props gracefully', () => {
    const minimalMeta = {
      title: 'Minimal Title',
      description: 'Minimal description',
      canonical: 'https://example.com/minimal',
    };

    const { container } = render(<SeoHead meta={minimalMeta} />);

    expect(container.querySelector('title')).toHaveTextContent('Minimal Title');
    expect(container.querySelector('meta[name="description"]')).toHaveAttribute(
      'content',
      'Minimal description'
    );
    expect(container.querySelector('link[rel="canonical"]')).toHaveAttribute(
      'href',
      'https://example.com/minimal'
    );

    // Should not render optional meta tags
    expect(container.querySelector('meta[name="keywords"]')).toBeNull();
    expect(container.querySelector('meta[property="og:image"]')).toBeNull();
    expect(container.querySelector('script[type="application/ld+json"]')).toBeNull();
  });

  it('uses fallback values for Open Graph when specific values are not provided', () => {
    const metaWithoutOG = {
      title: 'Test Title',
      description: 'Test Description',
      canonical: 'https://example.com/test',
    };

    const { container } = render(<SeoHead meta={metaWithoutOG} />);

    expect(container.querySelector('meta[property="og:title"]')).toHaveAttribute(
      'content',
      'Test Title'
    );
    expect(container.querySelector('meta[property="og:description"]')).toHaveAttribute(
      'content',
      'Test Description'
    );
    expect(container.querySelector('meta[property="og:type"]')).toHaveAttribute(
      'content',
      'website'
    );
  });

  it('uses fallback values for Twitter Card when specific values are not provided', () => {
    const metaWithoutTwitter = {
      title: 'Test Title',
      description: 'Test Description',
      canonical: 'https://example.com/test',
    };

    const { container } = render(<SeoHead meta={metaWithoutTwitter} />);

    expect(container.querySelector('meta[name="twitter:title"]')).toHaveAttribute(
      'content',
      'Test Title'
    );
    expect(container.querySelector('meta[name="twitter:description"]')).toHaveAttribute(
      'content',
      'Test Description'
    );
    expect(container.querySelector('meta[name="twitter:card"]')).toHaveAttribute(
      'content',
      'summary_large_image'
    );
  });

  it('renders viewport and charset meta tags', () => {
    const { container } = render(<SeoHead meta={mockMeta} />);

    expect(container.querySelector('meta[name="viewport"]')).toHaveAttribute(
      'content',
      'width=device-width, initial-scale=1'
    );
    expect(container.querySelector('meta[charset="utf-8"]')).toBeInTheDocument();
  });
});
