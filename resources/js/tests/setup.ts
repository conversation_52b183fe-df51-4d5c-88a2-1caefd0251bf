import '@testing-library/jest-dom';
import { vi } from 'vitest';
import React from 'react';

// Mock Inertia.js
vi.mock('@inertiajs/react', () => ({
  Link: ({ children, href, ...props }: Record<string, unknown>) => React.createElement('a', { href, ...props }, children),
  Head: ({ children }: { children: React.ReactNode }) => React.createElement('div', { 'data-testid': 'head' }, children),
  router: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
    visit: vi.fn(),
  },
  usePage: () => ({
    props: {},
    url: '/',
    component: 'TestComponent',
  }),
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock scrollTo
global.scrollTo = vi.fn();

// Mock Radix UI components
vi.mock('@radix-ui/react-select', () => ({
  Root: ({ children, onValueChange, value }: { children: React.ReactNode; onValueChange?: (value: string) => void; value?: string }) =>
    React.createElement('div', { 'data-testid': 'select-root', 'data-value': value }, children),
  Trigger: ({ children, ...props }: { children: React.ReactNode; [key: string]: unknown }) =>
    React.createElement('button', { 'data-testid': 'select-trigger', ...props }, children),
  Value: ({ children, placeholder }: { children?: React.ReactNode; placeholder?: string }) =>
    React.createElement('span', { 'data-testid': 'select-value' }, children || placeholder),
  Content: ({ children }: { children: React.ReactNode }) =>
    React.createElement('div', { 'data-testid': 'select-content' }, children),
  Item: ({ children, value, ...props }: { children: React.ReactNode; value: string; [key: string]: unknown }) =>
    React.createElement('div', { 'data-testid': 'select-item', 'data-value': value, ...props }, children),
  Icon: ({ children, asChild }: { children?: React.ReactNode; asChild?: boolean }) =>
    React.createElement('span', { 'data-testid': 'select-icon' }, children),
}));
